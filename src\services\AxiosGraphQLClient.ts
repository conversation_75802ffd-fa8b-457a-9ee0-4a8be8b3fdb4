import axios, { AxiosError, AxiosInstance, HttpStatusCode } from 'axios';
import { Variables } from 'graphql-request';
import { AUTH_KEYS } from 'constants/auth';
import { COMMON_MESSAGE, OPERATION_NAME, REACT_APP_API_URL } from 'constants/common';
import { clearStorage, getLocalStorage, removeLocalStorage, setLocalStorage } from 'utils/localStorage';
import includes from 'lodash/includes';
import { MicrosoftLoginRes, RefreshQuery } from '../types/User';
import { REFRESH } from './UserService';
import { showToast } from '../utils/common';

// Controller chính cho các request thông thường
const mainController = new AbortController();
// Controller riêng cho các request đăng nhập
const authController = new AbortController();

interface GraphQLErrorResponse {
    errors: {
        message: string;
        code: number;
        errors: {
            property: string;
            constraints: {
                [key: string]: string;
            };
        }[];
    }[];
    data: null;
}

interface GraphQLRequest {
    query: string;
    variables?: Variables;
    operationName?: string;
}

export default class AxiosGraphQLClient {
    private client: AxiosInstance;
    private authClient: AxiosInstance; // Client riêng cho các request đăng nhập
    private refreshToken: string;
    private accessToken: string;
    private readonly endpoint: string;
    private isRefreshTokenFailed: boolean = false;

    constructor() {
        this.endpoint = `${REACT_APP_API_URL}/graphql`;
        this.accessToken = getLocalStorage(AUTH_KEYS.ACCESS_TOKEN);
        this.refreshToken = getLocalStorage(AUTH_KEYS.REFRESH_TOKEN);
        this.client = this.createMainClient();
        this.authClient = this.createAuthClient();
    }

    private createMainClient(): AxiosInstance {
        return axios.create({
            baseURL: this.endpoint,
            headers: this.getHeaders(),
            signal: mainController.signal,
        });
    }

    private createAuthClient(): AxiosInstance {
        return axios.create({
            baseURL: this.endpoint,
            headers: this.getHeaders(),
            signal: authController.signal,
        });
    }

    private getHeaders(): Record<string, string> {
        const headers: Record<string, string> = {
            'Content-Type': 'application/json',
        };

        if (this.accessToken) {
            headers['Authorization'] = `Bearer ${this.accessToken}`;
        }

        return headers;
    }

    private updateHeaders(): void {
        this.client.defaults.headers.common = {
            ...this.client.defaults.headers.common,
            ...this.getHeaders(),
        };
        this.authClient.defaults.headers.common = {
            ...this.authClient.defaults.headers.common,
            ...this.getHeaders(),
        };
    }

    private extractOperationName(query: string): string {
        const operationMatch = query.match(/(?:query|mutation)\s+([A-Za-z0-9_]+)/);
        if (operationMatch && operationMatch[1]) {
            return operationMatch[1];
        }

        const fieldMatch = query.match(/{\s*([A-Za-z0-9_]+)/);
        if (fieldMatch && fieldMatch[1]) {
            return fieldMatch[1];
        }

        return '';
    }

    private async validateToken(operationName: string): Promise<void> {
        const publicOperations = ['login', 'register', 'forgotPassword', 'changePassword', 'resetPassword'];

        if (includes(publicOperations, operationName)) {
            return;
        }

        this.updateHeaders();
    }

    // tslint:disable-next-line: no-any
    public async request<T = any, V extends Variables = Variables>(
        query: string,
        variables?: V,
        operationName?: string
    ): Promise<T> {
        // Xác định các operation liên quan đến đăng nhập
        const isAuthOperation =
            operationName === OPERATION_NAME.LOGIN ||
            operationName === OPERATION_NAME.MICROSOFT_LOGIN_WITH_CODE ||
            query.includes('auth_get_azure_login_url');

        // Nếu không phải operation đăng nhập và refresh token đã thất bại
        if (!isAuthOperation && this.isRefreshTokenFailed) {
            throw new Error('Session expired. Please login again.');
        }

        const effectiveOperationName = this.extractOperationName(query);

        await this.validateToken(effectiveOperationName);

        if (operationName === OPERATION_NAME.LOGOUT) {
            this.accessToken = '';
            this.refreshToken = '';
            removeLocalStorage(AUTH_KEYS.ACCESS_TOKEN);
            removeLocalStorage(AUTH_KEYS.REFRESH_TOKEN);
        }

        try {
            const graphqlRequest: GraphQLRequest = {
                query,
                variables,
                operationName: effectiveOperationName || undefined,
            };

            // Sử dụng client phù hợp dựa trên loại operation
            const clientToUse = isAuthOperation ? this.authClient : this.client;
            const response = await clientToUse.post<{ data: T }>('', graphqlRequest);

            if (operationName === OPERATION_NAME.MICROSOFT_LOGIN_WITH_CODE) {
                const dataLogin = response.data.data as unknown as MicrosoftLoginRes;
                this.accessToken = dataLogin?.auth_login_with_azure_code?.access_token || '';
                this.refreshToken = dataLogin?.auth_login_with_azure_code?.refresh_token || '';
                setLocalStorage(AUTH_KEYS.ACCESS_TOKEN, this.accessToken);
                setLocalStorage(AUTH_KEYS.REFRESH_TOKEN, this.refreshToken);
                // Reset trạng thái lỗi khi đăng nhập thành công
                this.isRefreshTokenFailed = false;
            }

            return response.data.data;
            // tslint:disable-next-line: no-any
        } catch (error: any) {
            if (operationName === OPERATION_NAME.LOGOUT) {
                return {} as T;
            }

            if (error instanceof AxiosError) {
                const errorData: GraphQLErrorResponse = error?.response?.data;
                if (
                    operationName !== OPERATION_NAME.MICROSOFT_LOGIN_WITH_CODE &&
                    error.status === HttpStatusCode.Unauthorized
                ) {
                    try {
                        await this.handleRefreshToken();
                        return this.request<T>(query, variables, operationName);
                    } catch (refreshError) {
                        this.handleErrorRefreshToken();
                        showToast(false, [COMMON_MESSAGE.EXPIRED_TOKEN]);
                        throw new Error(COMMON_MESSAGE.EXPIRED_TOKEN);
                    }
                } else if (error.status === HttpStatusCode.BadRequest || error.status === HttpStatusCode.PayloadTooLarge) {
                    const listErrs: string[] = [];
                    if (errorData?.errors[0]?.errors) {
                        errorData.errors[0].errors.forEach((item) => {
                            Object.keys(item.constraints).forEach((key) => {
                                listErrs.push(item.constraints[key]);
                            });
                        });
                    } else {
                        listErrs.push(errorData?.errors[0]?.message);
                    }
                    showToast(false, listErrs);
                    throw errorData;
                } else {
                    const msgErr = errorData?.errors[0]?.message ?? COMMON_MESSAGE.ERROR_MESSAGE;
                    showToast(false, [msgErr]);
                    throw new Error(msgErr);
                }
            }

            return {} as T;
        }
    }

    private handleErrorRefreshToken(): void {
        this.accessToken = '';
        this.refreshToken = '';
        clearStorage();
        this.isRefreshTokenFailed = true;
        // Chỉ hủy các request thông thường, không hủy các request đăng nhập
        mainController.abort('Session expired');
    }

    private async handleRefreshToken(): Promise<void> {
        try {
            const graphqlRequest: GraphQLRequest = {
                query: REFRESH,
                variables: { refresh_token: this.refreshToken },
            };

            const response = await this.client.post<{ data: RefreshQuery }>('', graphqlRequest);

            if (response.data.data.auth_refresh_token) {
                this.accessToken = response.data.data.auth_refresh_token.access_token;
                this.refreshToken = response.data.data.auth_refresh_token.refresh_token;
                setLocalStorage(AUTH_KEYS.ACCESS_TOKEN, this.accessToken);
                setLocalStorage(AUTH_KEYS.REFRESH_TOKEN, this.refreshToken);
                this.updateHeaders();
                this.client = this.createMainClient();
                return;
            }

            this.handleErrorRefreshToken();
        } catch (error) {
            // tslint:disable-next-line: no-console
            console.error(error);
            this.handleErrorRefreshToken();
        }
    }

    public resetFailedState(): void {
        this.isRefreshTokenFailed = false;
    }
}

const axiosGraphQLClient = new AxiosGraphQLClient();
export { axiosGraphQLClient };
